{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "components/*": ["components/*"],
      "services/*": ["services/*"],
      "utils/*": ["utils/*"],
      "contexts/*": ["contexts/*"],
      "hooks/*": ["hooks/*"],
      "types/*": ["types/*"]
    },
    "types": ["vite/client", "node", "vitest/globals"],
    
    // Enhanced type checking for medical safety
    "exactOptionalPropertyTypes": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUncheckedIndexedAccess": true,
    "strictBindCallApply": true,
    "strictFunctionTypes": true,
    "strictNullChecks": true,
    "strictPropertyInitialization": true,
    "useUnknownInCatchVariables": true
  },
  "include": [
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.js",
    "src/**/*.jsx",
    "src/vitest.d.ts",
    "vite.config.js",
    "vitest.config.js"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "api"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
