/// <reference types="vitest/globals" />

import type {
  TestAPI,
  ExpectStatic,
  MockedFunction,
  MockInstance,
  SpyInstance
} from 'vitest';

declare global {
  const describe: TestAPI['describe'];
  const it: TestAPI['it'];
  const test: TestAPI['test'];
  const expect: ExpectStatic;
  const beforeAll: TestAPI['beforeAll'];
  const afterAll: TestAPI['afterAll'];
  const beforeEach: TestAPI['beforeEach'];
  const afterEach: TestAPI['afterEach'];
  const vi: typeof import('vitest')['vi'];

  // Additional globals that might be needed
  const suite: TestAPI['suite'];
  const bench: TestAPI['bench'];

  // Mock types
  type MockedFunction<T extends (...args: any[]) => any> = MockInstance<T>;

  // Global test utilities
  const fail: (message?: string) => never;
}

export {};
